import { FastifyReply } from 'fastify';
import * as statusService from '../services/status.service';
import { uploadSingleFile } from '../services/cloudinary.service';
import { ValidationError, NotFoundError } from '../utils/errors';
import { logger } from '../utils/logger';
import {
  CreateStatusRequest,
  ViewStatusRequest,
  AuthenticatedRequest
} from '../types';

export const createStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const statusData = request.body as CreateStatusRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!statusData.content_type) {
    return reply.status(400).send({
      success: false,
      message: 'Content type is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const status = await statusService.createStatus(userId, statusData);

    logger.info(`Status created for user ${userId}: ${status.id}`);

    return reply.status(201).send({
      success: true,
      message: 'Status created successfully',
      data: status
    });
  } catch (error: any) {
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error creating status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to create status'
    });
  }
};

export const uploadStatusMedia = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const mediaUrl = await uploadSingleFile(request, 'statusMedia');
    
    if (!mediaUrl) {
      return reply.status(400).send({
        success: false,
        message: 'No file uploaded'
      });
    }

    const userId = request.user.userId || request.user.phone;
    
    if (!request.body) {
      return reply.status(400).send({
        success: false,
        message: 'Request body is required'
      });
    }

    const body = request.body as any;
    const statusData: CreateStatusRequest = {
      content_type: mediaUrl.includes('.mp4') || mediaUrl.includes('.mov') ? 'video' : 'image',
      content_url: mediaUrl,
      caption: body?.caption || '',
      background_color: body?.background_color,
      text_color: body?.text_color,
      font_style: body?.font_style,
      privacy_setting: body?.privacy_setting || 'Contacts',
      allowed_contacts: body?.allowed_contacts || [],
      blocked_contacts: body?.blocked_contacts || []
    };

    const status = await statusService.createStatus(userId, statusData);

    logger.info(`Status with media created for user ${userId}: ${status.id}`);

    return reply.status(201).send({
      success: true,
      message: 'Status with media created successfully',
      data: status
    });
  } catch (error: any) {
    logger.error('Error uploading status media:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to upload status media'
    });
  }
};

export const getMyStatuses = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const statuses = await statusService.getUserStatuses(userId);

    return reply.status(200).send({
      success: true,
      message: 'Statuses retrieved successfully',
      data: statuses
    });
  } catch (error: any) {
    logger.error('Error getting my statuses:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve statuses'
    });
  }
};

export const getContactStatuses = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const summaries = await statusService.getContactStatuses(userId);

    return reply.status(200).send({
      success: true,
      message: 'Contact statuses retrieved successfully',
      data: summaries
    });
  } catch (error: any) {
    logger.error('Error getting contact statuses:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve contact statuses'
    });
  }
};

export const viewStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  const { status_id } = request.body as ViewStatusRequest;

  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!status_id) {
    return reply.status(400).send({
      success: false,
      message: 'Status ID is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const status = await statusService.viewStatus(status_id, userId);

    return reply.status(200).send({
      success: true,
      message: 'Status viewed successfully',
      data: status
    });
  } catch (error: any) {
    if (error instanceof NotFoundError) {
      return reply.status(404).send({
        success: false,
        message: error.message
      });
    }

    if (error instanceof ValidationError) {
      return reply.status(403).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error viewing status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to view status'
    });
  }
};

export const getUserStatuses = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  const { user_id } = request.params as { user_id: string };

  if (!user_id) {
    return reply.status(400).send({
      success: false,
      message: 'User ID is required'
    });
  }

  try {
    const statuses = await statusService.getUserStatuses(user_id);

    return reply.status(200).send({
      success: true,
      message: 'User statuses retrieved successfully',
      data: statuses
    });
  } catch (error: any) {
    logger.error('Error getting user statuses:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve user statuses'
    });
  }
};

export const getStatusViews = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  const { status_id } = request.params as { status_id: string };

  if (!status_id) {
    return reply.status(400).send({
      success: false,
      message: 'Status ID is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const views = await statusService.getStatusViews(status_id, userId);

    return reply.status(200).send({
      success: true,
      message: 'Status views retrieved successfully',
      data: views
    });
  } catch (error: any) {
    if (error instanceof NotFoundError) {
      return reply.status(404).send({
        success: false,
        message: error.message
      });
    }

    if (error instanceof ValidationError) {
      return reply.status(403).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error getting status views:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve status views'
    });
  }
};

export const deleteStatus = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  const { status_id } = request.params as { status_id: string };

  if (!status_id) {
    return reply.status(400).send({
      success: false,
      message: 'Status ID is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    await statusService.deleteStatus(status_id, userId);

    return reply.status(200).send({
      success: true,
      message: 'Status deleted successfully'
    });
  } catch (error: any) {
    if (error instanceof NotFoundError) {
      return reply.status(404).send({
        success: false,
        message: error.message
      });
    }

    if (error instanceof ValidationError) {
      return reply.status(403).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error deleting status:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to delete status'
    });
  }
};

export const getPrivacySettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const settings = await statusService.getPrivacySettings(userId);

    return reply.status(200).send({
      success: true,
      message: 'Privacy settings retrieved successfully',
      data: settings
    });
  } catch (error: any) {
    logger.error('Error getting privacy settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to retrieve privacy settings'
    });
  }
};

export const updatePrivacySettings = async (request: AuthenticatedRequest, reply: FastifyReply): Promise<any> => {
  if (!request.user?.phone) {
    return reply.status(401).send({
      success: false,
      message: 'User not authenticated'
    });
  }

  if (!request.body) {
    return reply.status(400).send({
      success: false,
      message: 'Request body is required'
    });
  }

  try {
    const userId = request.user.userId || request.user.phone;
    const settings = await statusService.updatePrivacySettings(userId, request.body as any);

    return reply.status(200).send({
      success: true,
      message: 'Privacy settings updated successfully',
      data: settings
    });
  } catch (error: any) {
    if (error instanceof ValidationError) {
      return reply.status(400).send({
        success: false,
        message: error.message
      });
    }

    logger.error('Error updating privacy settings:', error);
    return reply.status(500).send({
      success: false,
      message: 'Failed to update privacy settings'
    });
  }
};
