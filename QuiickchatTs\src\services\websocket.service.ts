import { FastifyInstance } from 'fastify';
import { Auth } from '../middleware/auth.middleware';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types';

interface ConnectedClient {
  socket: any;
  phone: string;
  subscriptions: Set<string>;
}

class WebSocketManager {
  private clients: Map<string, ConnectedClient> = new Map();
  private phoneToClientId: Map<string, string> = new Map();

  addClient(clientId: string, socket: any, phone: string): void {
    const client: ConnectedClient = {
      socket,
      phone,
      subscriptions: new Set()
    };

    this.clients.set(clientId, client);
    this.phoneToClientId.set(phone, clientId);
    
    logger.info(`Client connected: ${phone} (${clientId})`);
  }

  removeClient(clientId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      this.phoneToClientId.delete(client.phone);
      this.clients.delete(clientId);
      logger.info(`Client disconnected: ${client.phone} (${clientId})`);
    }
  }

  subscribeToContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.add(contactPhone);
      logger.info(`${client.phone} subscribed to ${contactPhone}`);
    }
  }

  unsubscribeFromContact(clientId: string, contactPhone: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(contactPhone);
      logger.info(`${client.phone} unsubscribed from ${contactPhone}`);
    }
  }

  broadcastStatusUpdate(userPhone: string, statusData: any): void {
    const subscriberClients = Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    );

    const message = JSON.stringify({
      type: 'status_update',
      user_phone: userPhone,
      data: statusData,
      timestamp: new Date().toISOString()
    });

    subscriberClients.forEach(client => {
      try {
        client.socket.send(message);
        logger.info(`Status update sent to ${client.phone} for ${userPhone}`);
      } catch (error) {
        logger.error(`Failed to send status update to ${client.phone}:`, error);
      }
    });
  }

  broadcastStatusView(statusId: string, viewerPhone: string, statusOwnerPhone: string): void {
    const ownerClientId = this.phoneToClientId.get(statusOwnerPhone);
    if (ownerClientId) {
      const ownerClient = this.clients.get(ownerClientId);
      if (ownerClient) {
        const message = JSON.stringify({
          type: 'status_viewed',
          status_id: statusId,
          viewer_phone: viewerPhone,
          timestamp: new Date().toISOString()
        });

        try {
          ownerClient.socket.send(message);
          logger.info(`Status view notification sent to ${statusOwnerPhone}`);
        } catch (error) {
          logger.error(`Failed to send view notification to ${statusOwnerPhone}:`, error);
        }
      }
    }
  }

  broadcastStatusDeletion(userPhone: string, statusId: string): void {
    const subscriberClients = Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    );

    const message = JSON.stringify({
      type: 'status_deleted',
      user_phone: userPhone,
      status_id: statusId,
      timestamp: new Date().toISOString()
    });

    subscriberClients.forEach(client => {
      try {
        client.socket.send(message);
        logger.info(`Status deletion sent to ${client.phone} for ${userPhone}`);
      } catch (error) {
        logger.error(`Failed to send status deletion to ${client.phone}:`, error);
      }
    });
  }

  getClientCount(): number {
    return this.clients.size;
  }

  getSubscriptionCount(userPhone: string): number {
    return Array.from(this.clients.values()).filter(
      client => client.subscriptions.has(userPhone)
    ).length;
  }

  sendQRStatusUpdate(sessionId: string, status: string, data?: any): void {
    const message = {
      type: 'qr_status_update',
      session_id: sessionId,
      status,
      data,
      timestamp: new Date().toISOString()
    };

    this.broadcast(JSON.stringify(message));
    logger.info(`QR status update sent for session ${sessionId}: ${status}`);
  }

  sendDeviceLinkUpdate(userPhone: string, deviceId: string, status: string, data?: any): void {
    const message = {
      type: 'device_link_update',
      device_id: deviceId,
      status,
      data,
      timestamp: new Date().toISOString()
    };

    this.sendToUser(userPhone, JSON.stringify(message));
    logger.info(`Device link update sent to ${userPhone} for device ${deviceId}: ${status}`);
  }

  sendSecurityAlert(userPhone: string, alertType: string, data: any): void {
    const message = {
      type: 'security_alert',
      alert_type: alertType,
      data,
      timestamp: new Date().toISOString()
    };

    this.sendToUser(userPhone, JSON.stringify(message));
    logger.info(`Security alert sent to ${userPhone}: ${alertType}`);
  }

  sendToUserDevices(userPhone: string, message: string): void {
    this.sendToUser(userPhone, message);
  }

  subscribeToQRSession(clientId: string, sessionId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.add(`qr_${sessionId}`);
      logger.info(`Client ${clientId} subscribed to QR session ${sessionId}`);
    }
  }

  unsubscribeFromQRSession(clientId: string, sessionId: string): void {
    const client = this.clients.get(clientId);
    if (client) {
      client.subscriptions.delete(`qr_${sessionId}`);
      logger.info(`Client ${clientId} unsubscribed from QR session ${sessionId}`);
    }
  }

  sendToQRSubscribers(sessionId: string, message: string): void {
    const subscriptionKey = `qr_${sessionId}`;

    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(subscriptionKey)) {
        try {
          client.socket.send(message);
        } catch (error) {
          logger.error(`Failed to send message to QR subscriber ${clientId}:`, error);
          this.removeClient(clientId);
        }
      }
    }
  }

  getClientPhone(clientId: string): string | null {
    const client = this.clients.get(clientId);
    return client ? client.phone : null;
  }

  broadcast(message: string): void {
    for (const [clientId, client] of this.clients.entries()) {
      try {
        client.socket.send(message);
      } catch (error) {
        logger.error(`Failed to broadcast message to client ${clientId}:`, error);
        this.removeClient(clientId);
      }
    }
  }

  sendToUser(userPhone: string, message: string): void {
    for (const [clientId, client] of this.clients.entries()) {
      if (client.subscriptions.has(userPhone)) {
        try {
          client.socket.send(message);
        } catch (error) {
          logger.error(`Failed to send message to user ${userPhone}, client ${clientId}:`, error);
          this.removeClient(clientId);
        }
      }
    }
  }
}

export const wsManager = new WebSocketManager();

export const setupWebSocket = async (fastify: FastifyInstance): Promise<void> => {
  await fastify.register(require('@fastify/websocket'));

  fastify.register(async function (fastify) {
    (fastify as any).get('/status-updates', { websocket: true }, (connection: any, _req: any) => {
      const clientId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

      connection.socket.on('message', async (message: any) => {
        try {
          const data = JSON.parse(message.toString());
          
          if (data.type === 'auth') {
            const token = data.token;
            if (!token) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Authentication token required'
              }));
              connection.socket.close();
              return;
            }

            try {
              const claims = Auth.validateAccessToken(token);
              wsManager.addClient(clientId, connection.socket, claims.phone);
              
              connection.socket.send(JSON.stringify({
                type: 'auth_success',
                message: 'Connected successfully'
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Invalid authentication token'
              }));
              connection.socket.close();
              return;
            }
          }

          if (data.type === 'subscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.subscribeToContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'subscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'unsubscribe') {
            const contactPhone = data.contact_phone;
            if (contactPhone) {
              wsManager.unsubscribeFromContact(clientId, contactPhone);
              connection.socket.send(JSON.stringify({
                type: 'unsubscribed',
                contact_phone: contactPhone
              }));
            }
          }

          if (data.type === 'ping') {
            connection.socket.send(JSON.stringify({
              type: 'pong',
              timestamp: new Date().toISOString()
            }));
          }

          if (data.type === 'subscribe_qr' && data.sessionId) {
            wsManager.subscribeToQRSession(clientId, data.sessionId);
            connection.socket.send(JSON.stringify({
              type: 'qr_subscribed',
              sessionId: data.sessionId
            }));
          }

          if (data.type === 'unsubscribe_qr' && data.sessionId) {
            wsManager.unsubscribeFromQRSession(clientId, data.sessionId);
            connection.socket.send(JSON.stringify({
              type: 'qr_unsubscribed',
              sessionId: data.sessionId
            }));
          }

          if (data.type === 'qr_status_check' && data.sessionId) {
            try {
              const qrAuthService = await import('./qr-auth.service');
              const status = await qrAuthService.getSessionStatus(data.sessionId);

              connection.socket.send(JSON.stringify({
                type: 'qr_status_response',
                sessionId: data.sessionId,
                status: status.status,
                expiresAt: status.expiresAt
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to check QR status'
              }));
            }
          }

          if (data.type === 'device_sync' && data.payload) {
            try {
              const encryptedSyncService = await import('./encrypted-sync.service');
              await encryptedSyncService.EncryptedSyncService.receiveSyncMessage(
                wsManager.getClientPhone(clientId) || '',
                data.payload
              );

              connection.socket.send(JSON.stringify({
                type: 'sync_received',
                messageId: data.payload.messageId
              }));
            } catch (error) {
              connection.socket.send(JSON.stringify({
                type: 'error',
                message: 'Failed to process sync message'
              }));
            }
          }

        } catch (error) {
          logger.error('WebSocket message error:', error);
          connection.socket.send(JSON.stringify({
            type: 'error',
            message: 'Invalid message format'
          }));
        }
      });

      connection.socket.on('close', () => {
        wsManager.removeClient(clientId);
      });

      connection.socket.on('error', (error: any) => {
        logger.error('WebSocket error:', error);
        wsManager.removeClient(clientId);
      });
    });
  });

  logger.info('WebSocket service initialized');
};
